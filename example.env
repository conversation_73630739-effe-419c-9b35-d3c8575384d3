EMAIL_FROM=<EMAIL>
EMAIL_SERVER_HOST="foo.bar.xyz"
EMAIL_SERVER_PASSWORD="password123"
EMAIL_SERVER_PORT="465"
EMAIL_SERVER_USER="<EMAIL>"
# Run `openssl rand -base64 32` to get a secure secret.
NEXTAUTH_SECRET=123456
NEXTAUTH_URL=https://A_REAL_DOMAIN_NAME
# Google OAuth credentials for NextAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
# Optional. Defaults to "." if left unset.
# You will need this on Docker/cloud native apps where
# you are required to attach storage volumes.
DATA_DIR=/data
# OpenAI API Key. You will need to sign up for an account and enter
# payment details.
OPENAI_API_KEY="Find this at https://platform.openai.com/account/api-keys"
# The name of your project in Google Cloud.
GOOGLE_CLOUD_PROJECT=my-gcs-project-change-this
# docker run -d \
#   -e POSTGRES_USER=prisma \
#   -e POSTGRES_PASSWORD=your_password \
#   -e POSTGRES_DB=prisma_dev \
#   -v ${PWD}/db:/var/lib/postgresql/data \
#   -p 5432:5432 \
#   postgres
POSTGRES_URI="postgres://prisma:your_password@localhost:5432/prisma_dev?schema=public"
# This is the path to the JSON file you downloaded from Google Cloud
# Learn more at
# https://cloud.google.com/docs/authentication/application-default-credentials#GAC
GOOGLE_APPLICATION_CREDENTIALS=key.json
# If you are running the app in prod, you can alternatively store
# The JSON and a literal value:
GCP_JSON_CREDS='{"foo": "bar"}'
GCS_BUCKET_NAME="where-to-store-binary-stuff"
# Running this app is expensive. Set this ENV if
# (and only if!) you want to stop new users from using the
# app. This is a good way to keep costs down.
AUTHORIZED_EMAILS=<EMAIL>
