// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URI")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String        @id @default(cuid())
  name          String?
  email         String?       @unique
  emailVerified DateTime?
  lastSeen      DateTime?
  image         String?
  createdAt     DateTime      @default(now())
  accounts      Account[]
  sessions      Session[]
  userSettings       UserSettings?
  Card               Card[]
  Deck               Deck[]
  writingSubmissions WritingSubmission[] // Added relation to WritingSubmission
}

model UserSettings {
  id                 Int      @id @default(autoincrement())
  user               User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId             String   @unique
  // Audio playback speed min: 0.5 max: 2
  playbackSpeed      Float    @default(1)
  // Cards/day max
  cardsPerDayMax     Int      @default(7)
  // Value from 0 to 1 representing the percentage liklihood
  // of a random playback of the user's audio.
  playbackPercentage Float    @default(0.125)
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt
  dailyWritingGoal   Int      @default(150)
  writingFirst       Boolean  @default(false) // Always quiz writing before reviewing cards
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Deck {
  id           Int     @id @default(autoincrement())
  langCode     String
  name         String
  userId       String
  user         User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  Card         Card[]
  published    Boolean @default(false)
  parentDeckId       Int?
  createdAt          DateTime @default(now())
  writingSubmissions WritingSubmission[] // Added relation to WritingSubmission

  @@unique([name, userId])
  @@index([userId])
}

model Card {
  id          Int      @id @default(autoincrement())
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId      String
  flagged     Boolean  @default(false)
  term        String
  definition  String
  langCode    String
  // Gender can be "M"ale, "F"emale, or "N"eutral
  gender      String   @default("N")
  createdAt   DateTime @default(now())
  Quiz        Quiz[]
  imageBlobId String?
  deckId      Int? // Nullable for gradual migration
  Deck        Deck?    @relation(fields: [deckId], references: [id])
  lastFailure Float    @default(0)

  @@unique([userId, term])
  @@index([deckId])
  @@index([userId])
}

model Quiz {
  id          Int    @id @default(autoincrement())
  cardId      Int
  Card        Card   @relation(fields: [cardId], references: [id], onDelete: Cascade)
  quizType    String
  stability   Float  @default(0)
  difficulty  Float  @default(0)
  firstReview Float  @default(0)
  lastReview  Float  @default(0)
  nextReview  Float  @default(0)
  lapses      Float  @default(0)
  repetitions Float  @default(0)

  @@unique([cardId, quizType])
  @@index([cardId])
  @@index([nextReview])
}

model TrainingData {
  id                 Int      @id @default(autoincrement())
  createdAt          DateTime @default(now())
  quizType           String
  yesNo              String
  explanation        String
  term               String
  definition         String
  langCode           String
  userInput          String
  englishTranslation String
}

model WritingSubmission {
  id                       Int      @id @default(autoincrement())
  user                     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId                   String
  deck                     Deck     @relation(fields: [deckId], references: [id], onDelete: Cascade)
  deckId                   Int
  prompt                   String
  submission               String   @db.Text // Use Text for potentially long submissions
  submissionCharacterCount Int
  correction               String   @db.Text // Use Text for potentially long corrections
  correctionCharacterCount Int
  createdAt                DateTime @default(now())

  @@index([userId])
  @@index([deckId])
  @@index([userId, createdAt]) // For querying daily progress
}
