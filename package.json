{"name": "temp-prisma", "version": "1.1.0-alpha", "description": "", "main": "index.js", "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "test": "jest", "test:coverage": "jest --coverage", "reset": "rm -rf node_modules && rm -rf package-lock.json && npm install && npx prisma migrate dev && npx tsx seeds/ingest.ts", "start": "npx prisma migrate deploy && next start", "format": "prettier --write pages/ && prettier --write koala/"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/node": "^20.14.2", "@types/nodemailer": "^6.4.15", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "eslint-plugin-rulesdir": "^0.2.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prisma": "^5.15.0", "typescript": "^5.4.5"}, "dependencies": {"@google-cloud/language": "^6.4.0", "@google-cloud/storage": "^7.11.2", "@google-cloud/text-to-speech": "^5.3.0", "@mantine/charts": "^7.17.4", "@mantine/core": "^7.17.4", "@mantine/form": "^7.17.4", "@mantine/hooks": "^7.17.4", "@mantine/modals": "^7.17.4", "@mantine/notifications": "^7.17.4", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.15.0", "@tabler/icons-react": "^3.6.0", "@tanstack/react-query": "^4.36.1", "@trpc/client": "^10.45.2", "@trpc/next": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@types/diff": "^6.0.0", "diff": "^7.0.0", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-config-next": "^14.2.4", "femto-fsrs": "^2.0.0", "next": "^14.2.4", "next-auth": "^4.24.7", "nodemailer": "^6.9.13", "openai": "^4.63.0", "posthog-js": "^1.252.1", "posthog-node": "^5.1.0", "radash": "^12.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "recharts": "^2.15.3", "superjson": "^2.2.1", "zod": "^3.23.8"}}