name: dart_srs
description: A comprehensive Dart implementation of the FSRS (Free Spaced Repetition Scheduler) algorithm for building spaced repetition applications.
version: 1.0.0
homepage: https://github.com/your-username/dart_srs
repository: https://github.com/your-username/dart_srs

environment:
  sdk: '>=2.17.0 <4.0.0'

dependencies:
  # No external dependencies - pure Dart implementation

dev_dependencies:
  test: ^1.21.0
  lints: ^2.0.0

# Optional: Add topics for pub.dev discoverability
topics:
  - spaced-repetition
  - fsrs
  - learning
  - memory
  - flashcards
  - education
  - algorithm

# Documentation
documentation: https://pub.dev/documentation/dart_srs/latest/

# Funding (optional)
funding:
  - https://github.com/sponsors/your-username

# Screenshots (optional - for pub.dev)
screenshots:
  - description: 'Basic usage example'
    path: screenshots/basic_usage.png
  - description: 'Grade preview functionality'
    path: screenshots/grade_preview.png
