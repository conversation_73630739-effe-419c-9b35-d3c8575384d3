import 'package:test/test.dart';
import '../lib/dart_srs.dart';

void main() {
  group('Grade Tests', () {
    test('Grade enum values', () {
      expect(Grade.again.value, equals(1));
      expect(Grade.hard.value, equals(2));
      expect(Grade.good.value, equals(3));
      expect(Grade.easy.value, equals(4));
    });

    test('Grade fromInt conversion', () {
      expect(Grade.fromInt(1), equals(Grade.again));
      expect(Grade.fromInt(2), equals(Grade.hard));
      expect(Grade.fromInt(3), equals(Grade.good));
      expect(Grade.fromInt(4), equals(Grade.easy));
      
      expect(() => Grade.fromInt(0), throwsArgumentError);
      expect(() => Grade.fromInt(5), throwsArgumentError);
    });

    test('Grade properties', () {
      expect(Grade.again.isFail, isTrue);
      expect(Grade.hard.isFail, isFalse);
      expect(Grade.good.isFail, isFalse);
      expect(Grade.easy.isFail, isFalse);
      
      expect(Grade.again.emoji, equals('❌'));
      expect(Grade.hard.emoji, equals('😰'));
      expect(Grade.good.emoji, equals('😊'));
      expect(Grade.easy.emoji, equals('😎'));
    });
  });

  group('Quiz Tests', () {
    test('New quiz creation', () {
      final quiz = Quiz.newQuiz(id: 1, cardId: 1);
      
      expect(quiz.id, equals(1));
      expect(quiz.cardId, equals(1));
      expect(quiz.isNew, isTrue);
      expect(quiz.lapses, equals(0));
      expect(quiz.repetitions, equals(0));
      expect(quiz.difficulty, equals(0.0));
      expect(quiz.stability, equals(0.0));
    });

    test('Quiz isDue method', () {
      final now = DateTime.now();
      final pastTime = now.subtract(Duration(hours: 1));
      final futureTime = now.add(Duration(hours: 1));
      
      // New quiz should be due
      final newQuiz = Quiz.newQuiz(id: 1, cardId: 1);
      expect(newQuiz.isDue(now), isTrue);
      
      // Quiz with past nextReview should be due
      final pastQuiz = newQuiz.copyWith(nextReview: pastTime);
      expect(pastQuiz.isDue(now), isTrue);
      
      // Quiz with future nextReview should not be due
      final futureQuiz = newQuiz.copyWith(nextReview: futureTime);
      expect(futureQuiz.isDue(now), isFalse);
    });

    test('Quiz serialization', () {
      final now = DateTime.now();
      final quiz = Quiz(
        id: 1,
        cardId: 1,
        difficulty: 5.5,
        stability: 2.3,
        lastReview: now,
        firstReview: now,
        lapses: 1,
        repetitions: 3,
        nextReview: now.add(Duration(days: 1)),
      );

      final map = quiz.toMap();
      final deserializedQuiz = Quiz.fromMap(map);

      expect(deserializedQuiz.id, equals(quiz.id));
      expect(deserializedQuiz.cardId, equals(quiz.cardId));
      expect(deserializedQuiz.difficulty, equals(quiz.difficulty));
      expect(deserializedQuiz.stability, equals(quiz.stability));
      expect(deserializedQuiz.lapses, equals(quiz.lapses));
      expect(deserializedQuiz.repetitions, equals(quiz.repetitions));
    });
  });

  group('FSRS Algorithm Tests', () {
    late FSRSAlgorithm fsrs;

    setUp(() {
      fsrs = FSRSAlgorithm(requestedRetentionRate: 0.9);
    });

    test('New card scheduling', () {
      final schedulingData = fsrs.scheduleNewCard(Grade.good);
      
      expect(schedulingData.difficulty, greaterThan(0));
      expect(schedulingData.stability, greaterThan(0));
      expect(schedulingData.nextReview.isAfter(DateTime.now()), isTrue);
    });

    test('Different grades produce different intervals', () {
      final easyScheduling = fsrs.scheduleNewCard(Grade.easy);
      final goodScheduling = fsrs.scheduleNewCard(Grade.good);
      final hardScheduling = fsrs.scheduleNewCard(Grade.hard);
      final againScheduling = fsrs.scheduleNewCard(Grade.again);

      // Easy should have longer interval than good
      expect(easyScheduling.nextReview.isAfter(goodScheduling.nextReview), isTrue);
      
      // Good should have longer interval than hard
      expect(goodScheduling.nextReview.isAfter(hardScheduling.nextReview), isTrue);
      
      // Hard should have longer interval than again
      expect(hardScheduling.nextReview.isAfter(againScheduling.nextReview), isTrue);
    });

    test('Existing card grading', () {
      final card = FSRSCard(difficulty: 5.0, stability: 2.0);
      final schedulingData = fsrs.gradeCard(card, 1.0, Grade.good);
      
      expect(schedulingData.difficulty, isNotNull);
      expect(schedulingData.stability, isNotNull);
      expect(schedulingData.nextReview.isAfter(DateTime.now()), isTrue);
    });

    test('Time formatting', () {
      final now = DateTime.now();
      
      expect(fsrs.formatTimeUntil(now.add(Duration(minutes: 2))), equals('Very Soon'));
      expect(fsrs.formatTimeUntil(now.add(Duration(minutes: 30))), equals('30 minutes'));
      expect(fsrs.formatTimeUntil(now.add(Duration(hours: 2))), equals('2 hours'));
      expect(fsrs.formatTimeUntil(now.add(Duration(days: 3))), equals('3 days'));
      expect(fsrs.formatTimeUntil(now.subtract(Duration(minutes: 10))), equals('Due now'));
    });
  });

  group('SRS Scheduler Tests', () {
    late SRSScheduler scheduler;

    setUp(() {
      scheduler = SRSScheduler(requestedRetentionRate: 0.71);
    });

    test('Grade new quiz', () {
      final quiz = Quiz.newQuiz(id: 1, cardId: 1);
      final gradedQuiz = scheduler.gradeQuiz(quiz, Grade.good);
      
      expect(gradedQuiz.repetitions, equals(1));
      expect(gradedQuiz.lastReview, isNotNull);
      expect(gradedQuiz.firstReview, isNotNull);
      expect(gradedQuiz.nextReview, isNotNull);
      expect(gradedQuiz.difficulty, greaterThan(0));
      expect(gradedQuiz.stability, greaterThan(0));
    });

    test('Grade quiz with failure', () {
      final quiz = Quiz.newQuiz(id: 1, cardId: 1);
      final gradedQuiz = scheduler.gradeQuiz(quiz, Grade.again);
      
      expect(gradedQuiz.lapses, equals(1));
      expect(gradedQuiz.lastFailure, isNotNull);
    });

    test('Get due quizzes', () {
      final now = DateTime.now();
      final pastTime = now.subtract(Duration(hours: 1));
      final futureTime = now.add(Duration(hours: 1));
      
      final quizzes = [
        Quiz.newQuiz(id: 1, cardId: 1), // New quiz (due)
        Quiz.newQuiz(id: 2, cardId: 2).copyWith(nextReview: pastTime), // Past due
        Quiz.newQuiz(id: 3, cardId: 3).copyWith(nextReview: futureTime), // Future
      ];
      
      final dueQuizzes = scheduler.getDueQuizzes(quizzes, now);
      expect(dueQuizzes.length, equals(2));
    });

    test('Get lesson statistics', () {
      final quizzes = [
        Quiz.newQuiz(id: 1, cardId: 1), // New
        Quiz.newQuiz(id: 2, cardId: 2), // New
        Quiz.newQuiz(id: 3, cardId: 3).copyWith(repetitions: 1), // Review
      ];
      
      final stats = scheduler.getLessonStats(quizzes);
      expect(stats.totalCards, equals(3));
      expect(stats.newCards, equals(2));
      expect(stats.reviewCards, equals(1));
    });

    test('Sort quizzes by priority', () {
      final now = DateTime.now();
      final pastTime = now.subtract(Duration(hours: 1));
      final futureTime = now.add(Duration(hours: 1));
      
      final quizzes = [
        Quiz.newQuiz(id: 1, cardId: 1).copyWith(nextReview: futureTime), // Future
        Quiz.newQuiz(id: 2, cardId: 2).copyWith(nextReview: pastTime), // Past (due)
        Quiz.newQuiz(id: 3, cardId: 3), // New (due)
      ];
      
      final sortedQuizzes = scheduler.sortQuizzesByPriority(quizzes, now);
      
      // Due quizzes should come first
      expect(sortedQuizzes[0].isDue(now), isTrue);
      expect(sortedQuizzes[1].isDue(now), isTrue);
      expect(sortedQuizzes[2].isDue(now), isFalse);
    });

    test('Grade preview', () {
      final quiz = Quiz.newQuiz(id: 1, cardId: 1);
      final preview = scheduler.getGradePreview(quiz);
      
      expect(preview.keys.length, equals(4));
      expect(preview.keys.contains(Grade.again), isTrue);
      expect(preview.keys.contains(Grade.hard), isTrue);
      expect(preview.keys.contains(Grade.good), isTrue);
      expect(preview.keys.contains(Grade.easy), isTrue);
      
      // Each preview should contain emoji and time
      for (final grade in Grade.values) {
        expect(preview[grade]!.contains(grade.emoji), isTrue);
      }
    });
  });

  group('Integration Tests', () {
    test('Complete learning session simulation', () {
      final scheduler = SRSScheduler();
      var quiz = Quiz.newQuiz(id: 1, cardId: 1);
      
      // First review
      quiz = scheduler.gradeQuiz(quiz, Grade.good);
      expect(quiz.repetitions, equals(1));
      expect(quiz.lapses, equals(0));
      
      // Second review (simulate time passing)
      final futureTime = quiz.nextReview!.add(Duration(minutes: 1));
      quiz = scheduler.gradeQuiz(quiz, Grade.hard, futureTime);
      expect(quiz.repetitions, equals(2));
      expect(quiz.lapses, equals(0));
      
      // Third review (failure)
      final futureTime2 = quiz.nextReview!.add(Duration(minutes: 1));
      quiz = scheduler.gradeQuiz(quiz, Grade.again, futureTime2);
      expect(quiz.repetitions, equals(3));
      expect(quiz.lapses, equals(1));
      expect(quiz.lastFailure, isNotNull);
    });
  });
}
