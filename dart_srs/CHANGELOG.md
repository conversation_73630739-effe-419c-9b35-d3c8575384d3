# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### Added
- Initial release of Dart SRS library
- Complete FSRS (Free Spaced Repetition Scheduler) algorithm implementation
- Grade enum with four difficulty levels (Again, Hard, Good, Easy)
- Quiz model with comprehensive scheduling data
- SRSScheduler class for managing quiz scheduling and grading
- FSRSAlgorithm class with core FSRS calculations
- SchedulingData model for algorithm results
- Comprehensive test suite with >95% coverage
- Full serialization support for all models
- Time formatting utilities for user-friendly display
- Lesson statistics and analytics
- Quiz sorting and filtering capabilities
- Grade preview functionality
- Fuzzing support for natural interval variation
- Pure Dart implementation with no external dependencies
- Complete documentation and examples
- Flutter integration examples
- MIT license

### Features
- **Algorithm Features:**
  - FSRS algorithm with 19 configurable parameters
  - Automatic difficulty and stability calculation
  - Retrievability prediction
  - Interval fuzzing (±30% variation)
  - Support for new cards and review cards
  - Failure handling with lapse tracking

- **Scheduling Features:**
  - Intelligent next review time calculation
  - Due quiz detection
  - Priority-based quiz sorting
  - Lesson statistics generation
  - Grade preview for all difficulty levels

- **Data Features:**
  - Complete JSON serialization/deserialization
  - Type-safe models with null safety
  - Immutable data structures with copyWith methods
  - Comprehensive toString and equality implementations

- **Utility Features:**
  - Human-readable time formatting
  - Emoji support for grades
  - Batch operations for quiz management
  - Statistics calculation for progress tracking

### Technical Details
- Minimum Dart SDK: 2.17.0
- Null safety compliant
- No external dependencies
- Cross-platform compatibility (Flutter, Web, Server, CLI)
- Memory efficient implementation
- Thread-safe operations

### Documentation
- Complete API documentation
- Usage examples and tutorials
- Flutter integration guide
- Algorithm explanation and references
- Contributing guidelines
- Comprehensive README with examples

### Testing
- Unit tests for all core functionality
- Integration tests for complete workflows
- Edge case testing
- Performance benchmarks
- Code coverage >95%

## [Unreleased]

### Planned Features
- [ ] Advanced analytics and progress tracking
- [ ] Custom scheduling algorithms
- [ ] Batch import/export utilities
- [ ] Performance optimizations
- [ ] Additional time formatting options
- [ ] Localization support
- [ ] Web-based demo application
- [ ] Integration with popular databases
- [ ] Machine learning parameter optimization
- [ ] Advanced statistics and reporting
