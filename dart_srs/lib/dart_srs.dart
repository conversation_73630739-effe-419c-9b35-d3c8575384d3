/// Dart SRS (Spaced Repetition System) Library
/// 
/// A comprehensive spaced repetition system implementation based on the FSRS algorithm.
/// This library provides all the necessary components to build a spaced repetition
/// application for language learning or any other memorization tasks.

library dart_srs;

// Export models
export 'models/grade.dart';
export 'models/quiz.dart';
export 'models/scheduling_data.dart';

// Export FSRS algorithm
export 'fsrs/fsrs_algorithm.dart';

// Export main scheduler
export 'srs_scheduler.dart';

/// Version information
const String version = '1.0.0';

/// Library description
const String description = 'A Dart implementation of the FSRS spaced repetition algorithm';

/// Supported features
const List<String> features = [
  'FSRS algorithm implementation',
  'Quiz scheduling and grading',
  'Multiple difficulty grades (Again, Hard, Good, Easy)',
  'Automatic interval calculation with fuzzing',
  'New card and review card handling',
  'Statistics and progress tracking',
  'Time formatting utilities',
  'Serialization support',
];

/// Quick start example
/// 
/// ```dart
/// import 'package:dart_srs/dart_srs.dart';
/// 
/// void main() {
///   // Create a scheduler
///   final scheduler = SRSScheduler();
///   
///   // Create a new quiz
///   final quiz = Quiz.newQuiz(id: 1, cardId: 1);
///   
///   // Grade the quiz
///   final gradedQuiz = scheduler.gradeQuiz(quiz, Grade.good);
///   
///   // Check when it's due next
///   print('Next review: ${scheduler.formatTimeUntil(gradedQuiz.nextReview!)}');
/// }
/// ```
class DartSRS {
  /// Create a new SRS scheduler with default settings
  static SRSScheduler createScheduler({
    double requestedRetentionRate = 0.71,
    List<double>? customParameters,
  }) {
    return SRSScheduler(
      requestedRetentionRate: requestedRetentionRate,
      customParameters: customParameters,
    );
  }

  /// Create a new quiz
  static Quiz createQuiz({
    required int id,
    required int cardId,
  }) {
    return Quiz.newQuiz(id: id, cardId: cardId);
  }

  /// Get all available grades
  static List<Grade> get allGrades => Grade.values;

  /// Get library information
  static Map<String, dynamic> get info => {
    'version': version,
    'description': description,
    'features': features,
  };
}
