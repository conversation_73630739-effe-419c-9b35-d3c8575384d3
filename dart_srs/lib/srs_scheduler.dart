import 'dart:math' as math;
import 'models/grade.dart';
import 'models/quiz.dart';
import 'models/scheduling_data.dart';
import 'fsrs/fsrs_algorithm.dart';

/// Main SRS Scheduler class that handles quiz scheduling and grading
class SRSScheduler {
  final FSRSAlgorithm _fsrs;

  SRSScheduler({
    double requestedRetentionRate = 0.71,
    List<double>? customParameters,
  }) : _fsrs = FSRSAlgorithm(
          requestedRetentionRate: requestedRetentionRate,
          parameters: customParameters ?? const [
            0.4072, 1.1829, 3.1262, 15.4722, 7.2102, 0.5316, 1.0651, 0.0234, 1.616,
            0.1544, 1.0824, 1.9813, 0.0953, 0.2975, 2.2042, 0.2407, 2.9466, 0.5034, 0.6567
          ],
        );

  /// Calculate scheduling data for a quiz based on grade
  SchedulingData calculateSchedulingData(
    Quiz quiz,
    Grade grade, [
    DateTime? now,
  ]) {
    now ??= DateTime.now();

    // Handle new cards
    if (quiz.isNew) {
      return _fsrs.scheduleNewCard(grade, now);
    }

    // Handle existing cards
    final fsrsCard = FSRSCard.fromQuiz(quiz);
    final daysSinceLastReview = quiz.lastReview != null
        ? now.difference(quiz.lastReview!).inMilliseconds / (24 * 60 * 60 * 1000)
        : 0.0;

    return _fsrs.gradeCard(fsrsCard, daysSinceLastReview, grade, now);
  }

  /// Grade a quiz and return updated quiz with new scheduling data
  Quiz gradeQuiz(
    Quiz quiz,
    Grade grade, [
    DateTime? now,
  ]) {
    now ??= DateTime.now();
    
    final schedulingData = calculateSchedulingData(quiz, grade, now);
    final isFail = grade.isFail;

    return quiz.copyWith(
      difficulty: schedulingData.difficulty,
      stability: schedulingData.stability,
      nextReview: schedulingData.nextReview,
      repetitions: quiz.repetitions + 1,
      lastReview: now,
      firstReview: quiz.firstReview ?? now,
      lastFailure: isFail ? now : quiz.lastFailure,
      lapses: quiz.lapses + (isFail ? 1 : 0),
    );
  }

  /// Get preview of next review times for all grades
  Map<Grade, String> getGradePreview(
    Quiz quiz, [
    DateTime? now,
  ]) {
    now ??= DateTime.now();

    if (quiz.isNew) {
      final options = <Grade, String>{};
      for (final grade in Grade.values) {
        final schedulingData = _fsrs.scheduleNewCard(grade, now);
        final timeString = _fsrs.formatTimeUntil(schedulingData.nextReview, now);
        options[grade] = "${grade.emoji} $timeString";
      }
      return options;
    }

    final fsrsCard = FSRSCard.fromQuiz(quiz);
    final daysSinceLastReview = quiz.lastReview != null
        ? now.difference(quiz.lastReview!).inMilliseconds / (24 * 60 * 60 * 1000)
        : 0.0;

    return _fsrs.getReviewOptionsFormatted(fsrsCard, daysSinceLastReview, now);
  }

  /// Check if a quiz is due for review
  bool isQuizDue(Quiz quiz, [DateTime? now]) {
    return quiz.isDue(now);
  }

  /// Get all due quizzes from a list
  List<Quiz> getDueQuizzes(List<Quiz> quizzes, [DateTime? now]) {
    now ??= DateTime.now();
    return quizzes.where((quiz) => quiz.isDue(now)).toList();
  }

  /// Get new quizzes (never reviewed) from a list
  List<Quiz> getNewQuizzes(List<Quiz> quizzes) {
    return quizzes.where((quiz) => quiz.isNew).toList();
  }

  /// Sort quizzes by priority (due first, then by next review time)
  List<Quiz> sortQuizzesByPriority(List<Quiz> quizzes, [DateTime? now]) {
    now ??= DateTime.now();
    
    return List<Quiz>.from(quizzes)..sort((a, b) {
      // Due quizzes first
      final aDue = a.isDue(now);
      final bDue = b.isDue(now);
      
      if (aDue && !bDue) return -1;
      if (!aDue && bDue) return 1;
      
      // If both due or both not due, sort by next review time
      if (a.nextReview == null && b.nextReview == null) return 0;
      if (a.nextReview == null) return -1;
      if (b.nextReview == null) return 1;
      
      return a.nextReview!.compareTo(b.nextReview!);
    });
  }

  /// Get lesson statistics
  LessonStats getLessonStats(List<Quiz> quizzes, [DateTime? now]) {
    now ??= DateTime.now();
    
    final totalCards = quizzes.length;
    final dueQuizzes = quizzes.where((quiz) => quiz.isDue(now)).length;
    final newCards = quizzes.where((quiz) => quiz.isNew).length;
    final reviewCards = totalCards - newCards;
    
    return LessonStats(
      totalCards: totalCards,
      dueQuizzes: dueQuizzes,
      newCards: newCards,
      reviewCards: reviewCards,
    );
  }

  /// Format time until next review
  String formatTimeUntil(DateTime nextReview, [DateTime? now]) {
    return _fsrs.formatTimeUntil(nextReview, now);
  }
}

/// Statistics for a lesson or deck
class LessonStats {
  final int totalCards;
  final int dueQuizzes;
  final int newCards;
  final int reviewCards;

  const LessonStats({
    required this.totalCards,
    required this.dueQuizzes,
    required this.newCards,
    required this.reviewCards,
  });

  @override
  String toString() {
    return 'LessonStats(total: $totalCards, due: $dueQuizzes, '
           'new: $newCards, review: $reviewCards)';
  }
}
