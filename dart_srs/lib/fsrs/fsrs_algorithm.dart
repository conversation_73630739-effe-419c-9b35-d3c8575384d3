import 'dart:math' as math;

import '../models/grade.dart';
import '../models/scheduling_data.dart';

/// FSRS (Free Spaced Repetition Scheduler) Algorithm Implementation
/// Based on the original FSRS algorithm with default parameters
class FSRSAlgorithm {
  // Default FSRS parameters
  static const List<double> _defaultParameters = [
    0.4072,
    1.1829,
    3.1262,
    15.4722,
    7.2102,
    0.5316,
    1.0651,
    0.0234,
    1.616,
    0.1544,
    1.0824,
    1.9813,
    0.0953,
    0.2975,
    2.2042,
    0.2407,
    2.9466,
    0.5034,
    0.6567,
  ];

  final List<double> parameters;
  final double requestedRetentionRate;

  const FSRSAlgorithm({
    this.parameters = _defaultParameters,
    this.requestedRetentionRate = 0.9,
  });

  /// Calculate scheduling data for a new card
  SchedulingData scheduleNewCard(Grade grade, [DateTime? now]) {
    now ??= DateTime.now();

    final difficulty = _initDifficulty(grade);
    final stability = _initStability(grade);
    final interval = _calculateInterval(stability);

    return SchedulingData(
      difficulty: difficulty,
      stability: stability,
      nextReview: now.add(
        Duration(milliseconds: _fuzzInterval(interval).round()),
      ),
    );
  }

  /// Calculate scheduling data for an existing card
  SchedulingData gradeCard(
    FSRSCard card,
    double daysSinceLastReview,
    Grade grade, [
    DateTime? now,
  ]) {
    now ??= DateTime.now();

    final retrievability = _calculateRetrievability(
      card.stability,
      daysSinceLastReview,
    );
    final difficulty = _updateDifficulty(card.difficulty, grade);
    final stability = _updateStability(
      card,
      daysSinceLastReview,
      grade,
      retrievability,
    );
    final interval = _calculateInterval(stability);

    return SchedulingData(
      difficulty: difficulty,
      stability: stability,
      nextReview: now.add(
        Duration(milliseconds: _fuzzInterval(interval).round()),
      ),
    );
  }

  /// Calculate initial difficulty for a new card
  double _initDifficulty(Grade grade) {
    return math.max(
      math.min(parameters[4] - parameters[5] * (grade.value - 3), 10),
      1,
    );
  }

  /// Calculate initial stability for a new card
  double _initStability(Grade grade) {
    return math.max(parameters[grade.value - 1], 0.1);
  }

  /// Update difficulty based on grade
  double _updateDifficulty(double difficulty, Grade grade) {
    final deltaD = -parameters[6] * (grade.value - 3);
    return math.max(math.min(difficulty + deltaD, 10), 1);
  }

  /// Update stability based on card state and grade
  double _updateStability(
    FSRSCard card,
    double daysSinceLastReview,
    Grade grade,
    double retrievability,
  ) {
    double newStability;

    if (grade == Grade.again) {
      newStability =
          parameters[11] *
          math.pow(card.difficulty, -parameters[12]) *
          (math.pow(card.stability + 1, parameters[13]) - 1) *
          math.exp((1 - retrievability) * parameters[14]);
    } else {
      final stressReductionFactor = math.exp(
        (1 - retrievability) * parameters[8],
      );
      final difficultyFactor =
          (11 - card.difficulty) / (11 - card.difficulty * parameters[15]);

      if (daysSinceLastReview > 0) {
        final stabilityIncrease = difficultyFactor * stressReductionFactor - 1;
        newStability = card.stability * (1 + stabilityIncrease);
      } else {
        newStability = card.stability;
      }
    }

    return math.max(newStability, 0.1);
  }

  /// Calculate retrievability (probability of recall)
  double _calculateRetrievability(
    double stability,
    double daysSinceLastReview,
  ) {
    return math.pow(1 + daysSinceLastReview / (9 * stability), -1).toDouble();
  }

  /// Calculate interval in milliseconds from stability
  double _calculateInterval(double stability) {
    const millisecondsPerDay = 24 * 60 * 60 * 1000;
    return stability * millisecondsPerDay;
  }

  /// Add fuzzing to interval (±30% random variation)
  double _fuzzInterval(double interval) {
    final fuzzRange = interval * 0.3;
    final fuzzFactor = (math.Random().nextDouble() * 2 - 1) * fuzzRange;
    return interval + fuzzFactor;
  }

  /// Get next review options for all grades with formatted display
  Map<Grade, String> getReviewOptionsFormatted(
    FSRSCard card,
    double daysSinceLastReview, [
    DateTime? now,
  ]) {
    now ??= DateTime.now();

    final options = <Grade, String>{};

    for (final grade in Grade.values) {
      final schedulingData = gradeCard(card, daysSinceLastReview, grade, now);
      final timeString = formatTimeUntil(schedulingData.nextReview, now);
      options[grade] = "${grade.emoji} $timeString";
    }

    return options;
  }

  /// Get next review options for all grades
  Map<Grade, SchedulingData> getReviewOptions(
    FSRSCard card,
    double daysSinceLastReview, [
    DateTime? now,
  ]) {
    now ??= DateTime.now();

    final options = <Grade, SchedulingData>{};

    for (final grade in Grade.values) {
      options[grade] = gradeCard(card, daysSinceLastReview, grade, now);
    }

    return options;
  }

  /// Format time until next review for display
  String formatTimeUntil(DateTime nextReview, [DateTime? now]) {
    now ??= DateTime.now();

    final difference = nextReview.difference(now);

    if (difference.isNegative) {
      return "Due now";
    }

    final minutes = difference.inMinutes;

    if (minutes < 5) {
      return "Very Soon";
    } else if (minutes < 60) {
      return "${minutes} minute${minutes == 1 ? '' : 's'}";
    } else if (minutes < 24 * 60) {
      final hours = (minutes / 60).floor();
      return "${hours} hour${hours == 1 ? '' : 's'}";
    } else if (minutes < 30 * 24 * 60) {
      final days = (minutes / (24 * 60)).floor();
      return "${days} day${days == 1 ? '' : 's'}";
    } else {
      final months = (minutes / (30 * 24 * 60)).floor();
      return "${months} month${months == 1 ? '' : 's'}";
    }
  }
}
