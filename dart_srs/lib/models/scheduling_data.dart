/// Represents the scheduling data calculated by FSRS algorithm
class SchedulingData {
  final double difficulty;
  final double stability;
  final DateTime nextReview;

  const SchedulingData({
    required this.difficulty,
    required this.stability,
    required this.nextReview,
  });

  /// Create a copy with updated values
  SchedulingData copyWith({
    double? difficulty,
    double? stability,
    DateTime? nextReview,
  }) {
    return SchedulingData(
      difficulty: difficulty ?? this.difficulty,
      stability: stability ?? this.stability,
      nextReview: nextReview ?? this.nextReview,
    );
  }

  /// Convert to map for serialization
  Map<String, dynamic> toMap() {
    return {
      'difficulty': difficulty,
      'stability': stability,
      'nextReview': nextReview.millisecondsSinceEpoch,
    };
  }

  /// Create from map for deserialization
  factory SchedulingData.fromMap(Map<String, dynamic> map) {
    return SchedulingData(
      difficulty: map['difficulty']?.toDouble() ?? 0.0,
      stability: map['stability']?.toDouble() ?? 0.0,
      nextReview: DateTime.fromMillisecondsSinceEpoch(map['nextReview']),
    );
  }

  @override
  String toString() {
    return 'SchedulingData(difficulty: $difficulty, stability: $stability, '
           'nextReview: $nextReview)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SchedulingData &&
        other.difficulty == difficulty &&
        other.stability == stability &&
        other.nextReview == nextReview;
  }

  @override
  int get hashCode => Object.hash(difficulty, stability, nextReview);
}

/// Represents FSRS card state for algorithm calculations
class FSRSCard {
  final double difficulty;  // D
  final double stability;   // S

  const FSRSCard({
    required this.difficulty,
    required this.stability,
  });

  /// Create from Quiz
  factory FSRSCard.fromQuiz(Quiz quiz) {
    return FSRSCard(
      difficulty: quiz.difficulty,
      stability: quiz.stability,
    );
  }

  @override
  String toString() {
    return 'FSRSCard(D: $difficulty, S: $stability)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FSRSCard &&
        other.difficulty == difficulty &&
        other.stability == stability;
  }

  @override
  int get hashCode => Object.hash(difficulty, stability);
}
