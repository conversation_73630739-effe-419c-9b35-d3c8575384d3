import '../lib/dart_srs.dart';

void main() {
  print('=== Dart SRS Example ===\n');
  
  // Create a scheduler with default settings
  final scheduler = SRSScheduler(requestedRetentionRate: 0.71);
  
  // Create some sample quizzes
  final quiz1 = Quiz.newQuiz(id: 1, cardId: 1);
  final quiz2 = Quiz.newQuiz(id: 2, cardId: 2);
  final quiz3 = Quiz.newQuiz(id: 3, cardId: 3);
  
  print('📚 Initial Quizzes:');
  print('Quiz 1: ${quiz1.isNew ? "New" : "Review"} - Due: ${quiz1.isDue()}');
  print('Quiz 2: ${quiz2.isNew ? "New" : "Review"} - Due: ${quiz2.isDue()}');
  print('Quiz 3: ${quiz3.isNew ? "New" : "Review"} - Due: ${quiz3.isDue()}\n');
  
  // Grade the first quiz as "Good"
  print('🎯 Grading Quiz 1 as "Good"...');
  final gradedQuiz1 = scheduler.gradeQuiz(quiz1, Grade.good);
  print('Next review: ${scheduler.formatTimeUntil(gradedQuiz1.nextReview!)}');
  print('Difficulty: ${gradedQuiz1.difficulty.toStringAsFixed(2)}');
  print('Stability: ${gradedQuiz1.stability.toStringAsFixed(2)}');
  print('Repetitions: ${gradedQuiz1.repetitions}\n');
  
  // Grade the second quiz as "Hard"
  print('😰 Grading Quiz 2 as "Hard"...');
  final gradedQuiz2 = scheduler.gradeQuiz(quiz2, Grade.hard);
  print('Next review: ${scheduler.formatTimeUntil(gradedQuiz2.nextReview!)}');
  print('Difficulty: ${gradedQuiz2.difficulty.toStringAsFixed(2)}');
  print('Stability: ${gradedQuiz2.stability.toStringAsFixed(2)}');
  print('Repetitions: ${gradedQuiz2.repetitions}\n');
  
  // Grade the third quiz as "Again" (failed)
  print('❌ Grading Quiz 3 as "Again" (failed)...');
  final gradedQuiz3 = scheduler.gradeQuiz(quiz3, Grade.again);
  print('Next review: ${scheduler.formatTimeUntil(gradedQuiz3.nextReview!)}');
  print('Difficulty: ${gradedQuiz3.difficulty.toStringAsFixed(2)}');
  print('Stability: ${gradedQuiz3.stability.toStringAsFixed(2)}');
  print('Repetitions: ${gradedQuiz3.repetitions}');
  print('Lapses: ${gradedQuiz3.lapses}\n');
  
  // Show grade preview for a new quiz
  print('🔮 Grade Preview for New Quiz:');
  final gradePreview = scheduler.getGradePreview(Quiz.newQuiz(id: 4, cardId: 4));
  for (final grade in Grade.values) {
    print('  ${gradePreview[grade]}');
  }
  print();
  
  // Show grade preview for an existing quiz
  print('🔮 Grade Preview for Existing Quiz (Quiz 1):');
  final existingPreview = scheduler.getGradePreview(gradedQuiz1);
  for (final grade in Grade.values) {
    print('  ${existingPreview[grade]}');
  }
  print();
  
  // Demonstrate lesson statistics
  final allQuizzes = [gradedQuiz1, gradedQuiz2, gradedQuiz3, Quiz.newQuiz(id: 4, cardId: 4)];
  final stats = scheduler.getLessonStats(allQuizzes);
  print('📊 Lesson Statistics:');
  print('Total cards: ${stats.totalCards}');
  print('Due quizzes: ${stats.dueQuizzes}');
  print('New cards: ${stats.newCards}');
  print('Review cards: ${stats.reviewCards}\n');
  
  // Show due quizzes
  final dueQuizzes = scheduler.getDueQuizzes(allQuizzes);
  print('⏰ Due Quizzes (${dueQuizzes.length}):');
  for (final quiz in dueQuizzes) {
    print('  Quiz ${quiz.id}: ${quiz.isNew ? "New" : "Review"}');
  }
  print();
  
  // Show sorted quizzes by priority
  final sortedQuizzes = scheduler.sortQuizzesByPriority(allQuizzes);
  print('📋 Quizzes Sorted by Priority:');
  for (final quiz in sortedQuizzes) {
    final status = quiz.isNew ? "New" : (quiz.isDue() ? "Due" : "Scheduled");
    final nextReview = quiz.nextReview != null 
        ? scheduler.formatTimeUntil(quiz.nextReview!)
        : "Not scheduled";
    print('  Quiz ${quiz.id}: $status - Next: $nextReview');
  }
  print();
  
  // Demonstrate serialization
  print('💾 Serialization Example:');
  final quizMap = gradedQuiz1.toMap();
  print('Quiz 1 serialized: ${quizMap.keys.join(", ")}');
  
  final deserializedQuiz = Quiz.fromMap(quizMap);
  print('Quiz 1 deserialized: ID=${deserializedQuiz.id}, Repetitions=${deserializedQuiz.repetitions}');
  print();
  
  // Show library info
  print('ℹ️ Library Information:');
  final info = DartSRS.info;
  print('Version: ${info['version']}');
  print('Description: ${info['description']}');
  print('Features: ${(info['features'] as List).length} features available');
  
  print('\n=== Example Complete ===');
}

/// Helper function to demonstrate advanced usage
void demonstrateAdvancedUsage() {
  print('\n=== Advanced Usage Demo ===');
  
  // Create scheduler with custom parameters
  final customScheduler = SRSScheduler(
    requestedRetentionRate: 0.85,
    customParameters: [
      0.5, 1.2, 3.0, 15.0, 7.0, 0.5, 1.0, 0.02, 1.6,
      0.15, 1.1, 2.0, 0.1, 0.3, 2.2, 0.25, 3.0, 0.5, 0.65
    ],
  );
  
  // Simulate a learning session
  var quiz = Quiz.newQuiz(id: 1, cardId: 1);
  
  print('📖 Simulating Learning Session:');
  print('Initial quiz: New card');
  
  // First review - user finds it easy
  quiz = customScheduler.gradeQuiz(quiz, Grade.easy);
  print('After Easy grade: Next review in ${customScheduler.formatTimeUntil(quiz.nextReview!)}');
  
  // Simulate time passing and second review - user finds it good
  final futureTime = quiz.nextReview!.add(Duration(minutes: 1));
  quiz = customScheduler.gradeQuiz(quiz, Grade.good, futureTime);
  print('After Good grade: Next review in ${customScheduler.formatTimeUntil(quiz.nextReview!, futureTime)}');
  
  // Third review - user struggles
  final futureTime2 = quiz.nextReview!.add(Duration(minutes: 1));
  quiz = customScheduler.gradeQuiz(quiz, Grade.again, futureTime2);
  print('After Again grade: Next review in ${customScheduler.formatTimeUntil(quiz.nextReview!, futureTime2)}');
  
  print('Final stats: Repetitions=${quiz.repetitions}, Lapses=${quiz.lapses}');
}
