# Dart SRS - Spaced Repetition System

[![Pub Version](https://img.shields.io/pub/v/dart_srs.svg)](https://pub.dev/packages/dart_srs)
[![Dart SDK Version](https://badgen.net/pub/sdk-version/dart_srs)](https://pub.dev/packages/dart_srs)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A comprehensive Dart implementation of the **FSRS (Free Spaced Repetition Scheduler)** algorithm for building spaced repetition applications. Perfect for language learning apps, flashcard systems, and any application that needs intelligent review scheduling.

## 🌟 Features

- **FSRS Algorithm**: Complete implementation of the state-of-the-art FSRS algorithm
- **Multiple Difficulty Grades**: Support for Again, Hard, Good, and Easy ratings
- **Automatic Scheduling**: Intelligent calculation of next review times
- **Fuzzing Support**: Adds natural variation to review intervals
- **Statistics & Analytics**: Comprehensive lesson and progress statistics
- **Serialization**: Full JSON serialization support for persistence
- **Pure Dart**: No external dependencies, works on all Dart platforms
- **Well Tested**: Comprehensive test suite with >95% coverage
- **Type Safe**: Full null safety and strong typing

## 🚀 Quick Start

Add this to your `pubspec.yaml`:

```yaml
dependencies:
  dart_srs: ^1.0.0
```

Then run:

```bash
dart pub get
```

## 📖 Basic Usage

```dart
import 'package:dart_srs/dart_srs.dart';

void main() {
  // Create a scheduler
  final scheduler = SRSScheduler();
  
  // Create a new quiz/card
  final quiz = Quiz.newQuiz(id: 1, cardId: 1);
  
  // Grade the quiz based on user performance
  final gradedQuiz = scheduler.gradeQuiz(quiz, Grade.good);
  
  // Check when it's due for next review
  print('Next review: ${scheduler.formatTimeUntil(gradedQuiz.nextReview!)}');
  // Output: Next review: 10 minutes
  
  // Get preview of all possible grades
  final preview = scheduler.getGradePreview(quiz);
  preview.forEach((grade, timeString) {
    print('$timeString');
  });
  // Output:
  // ❌ Very Soon
  // 😰 6 minutes  
  // 😊 10 minutes
  // 😎 4 days
}
```

## 🎯 Core Concepts

### Grades

The system supports four difficulty grades:

```dart
enum Grade {
  again,  // ❌ Failed to recall (1)
  hard,   // 😰 Recalled with difficulty (2)  
  good,   // 😊 Recalled correctly (3)
  easy,   // 😎 Recalled easily (4)
}
```

### Quiz States

- **New Cards**: Never been reviewed (`quiz.isNew == true`)
- **Review Cards**: Previously studied cards
- **Due Cards**: Cards ready for review (`quiz.isDue() == true`)

### Scheduling Data

Each quiz contains FSRS scheduling parameters:

- **Difficulty**: How hard the card is (1-10)
- **Stability**: How well it's memorized (days)
- **Next Review**: When to review next
- **Repetitions**: Total review count
- **Lapses**: Number of failures

## 🔧 Advanced Usage

### Custom Parameters

```dart
// Create scheduler with custom FSRS parameters
final scheduler = SRSScheduler(
  requestedRetentionRate: 0.85, // Target 85% retention
  customParameters: [
    0.5, 1.2, 3.0, 15.0, 7.0, 0.5, 1.0, 0.02, 1.6,
    0.15, 1.1, 2.0, 0.1, 0.3, 2.2, 0.25, 3.0, 0.5, 0.65
  ],
);
```

### Lesson Management

```dart
// Get lesson statistics
final stats = scheduler.getLessonStats(quizzes);
print('Total: ${stats.totalCards}');
print('Due: ${stats.dueQuizzes}');
print('New: ${stats.newCards}');

// Get only due quizzes
final dueQuizzes = scheduler.getDueQuizzes(allQuizzes);

// Sort by priority (due first)
final sortedQuizzes = scheduler.sortQuizzesByPriority(allQuizzes);
```

### Persistence

```dart
// Serialize quiz to JSON
final quizMap = quiz.toMap();
final jsonString = jsonEncode(quizMap);

// Deserialize from JSON
final quizMap = jsonDecode(jsonString);
final quiz = Quiz.fromMap(quizMap);
```

### Learning Session Simulation

```dart
void simulateLearningSession() {
  final scheduler = SRSScheduler();
  var quiz = Quiz.newQuiz(id: 1, cardId: 1);
  
  // First review - user finds it good
  quiz = scheduler.gradeQuiz(quiz, Grade.good);
  print('After good: ${scheduler.formatTimeUntil(quiz.nextReview!)}');
  
  // Simulate time passing and review again
  final futureTime = quiz.nextReview!.add(Duration(minutes: 1));
  quiz = scheduler.gradeQuiz(quiz, Grade.easy, futureTime);
  print('After easy: ${scheduler.formatTimeUntil(quiz.nextReview!, futureTime)}');
  
  // User struggles this time
  final futureTime2 = quiz.nextReview!.add(Duration(minutes: 1));
  quiz = scheduler.gradeQuiz(quiz, Grade.again, futureTime2);
  print('After failure: ${scheduler.formatTimeUntil(quiz.nextReview!, futureTime2)}');
  
  print('Stats: ${quiz.repetitions} repetitions, ${quiz.lapses} lapses');
}
```

## 🧪 Testing

Run the test suite:

```bash
dart test
```

The library includes comprehensive tests covering:
- Grade enum functionality
- Quiz creation and management
- FSRS algorithm correctness
- Scheduler operations
- Serialization/deserialization
- Integration scenarios

## 📊 Algorithm Details

This implementation is based on the **FSRS algorithm** which:

1. **Calculates Difficulty**: Based on user performance history
2. **Estimates Stability**: How long information stays in memory
3. **Predicts Retrievability**: Probability of successful recall
4. **Schedules Reviews**: Optimizes review timing for retention

The algorithm uses 19 parameters that can be customized based on your specific use case and user data.

## 🎨 Flutter Integration

Perfect for Flutter apps:

```dart
class StudyScreen extends StatefulWidget {
  @override
  _StudyScreenState createState() => _StudyScreenState();
}

class _StudyScreenState extends State<StudyScreen> {
  final scheduler = SRSScheduler();
  Quiz? currentQuiz;
  
  void gradeCurrentQuiz(Grade grade) {
    if (currentQuiz != null) {
      final gradedQuiz = scheduler.gradeQuiz(currentQuiz!, grade);
      // Save to database
      saveQuiz(gradedQuiz);
      // Load next quiz
      loadNextQuiz();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Quiz content
          QuizCard(quiz: currentQuiz),
          // Grade buttons
          Row(
            children: Grade.values.map((grade) => 
              ElevatedButton(
                onPressed: () => gradeCurrentQuiz(grade),
                child: Text('${grade.emoji} ${grade.description}'),
              )
            ).toList(),
          ),
        ],
      ),
    );
  }
}
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Based on the [FSRS algorithm](https://github.com/open-spaced-repetition/fsrs4anki) by Jarrett Ye
- Inspired by the original KoalaCards TypeScript implementation
- Thanks to the spaced repetition research community

## 📚 References

- [FSRS Algorithm Paper](https://www.nature.com/articles/s41598-024-54354-0)
- [Spaced Repetition Wikipedia](https://en.wikipedia.org/wiki/Spaced_repetition)
- [Forgetting Curve](https://en.wikipedia.org/wiki/Forgetting_curve)
